<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quotes about Category | Wisdom Collection - quotese.com</title>
    <meta name="description" content="Explore our collection of quotes about this category. Find inspiration, wisdom, and profound insights on this topic from great minds throughout history.">
    <meta name="keywords" content="quotes about category, category quotes, thematic quotes, wisdom by topic, inspirational quotes">
    <link rel="canonical" href="https://quotese.com/categories/">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Quotes about Category | Wisdom Collection - quotese.com">
    <meta property="og:description" content="Explore our collection of quotes about this category. Find inspiration, wisdom, and profound insights on this topic from great minds throughout history.">
    <meta property="og:image" content="https://quotese.com/images/og-image-category.jpg">
    <meta property="og:url" content="https://quotese.com/categories/">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="quotese.com">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@quotesecom">
    <meta name="twitter:title" content="Quotes about Category | Wisdom Collection">
    <meta name="twitter:description" content="Explore our collection of quotes about this category. Find inspiration, wisdom, and profound insights on this topic from great minds throughout history.">
    <meta name="twitter:image" content="https://quotese.com/images/og-image-category.jpg">
    <!-- Tailwind CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif:wght@400;500;600;700&family=Noto+Sans:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/styles.css" rel="stylesheet">
    <link href="css/animations.css" rel="stylesheet">
    <!-- Google Analytics -->
    <script src="js/analytics.js"></script>
</head>
<body class="light-mode">
    <!-- 导航栏 (将由组件加载器加载) -->
    <header id="navigation-container" role="banner"></header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 py-8" role="main">
        <!-- 面包屑导航 -->
        <div id="breadcrumb-container"></div>
        <!-- Page Header - 隐藏 -->
        <section class="mb-8 text-center py-4 hidden" id="category-header">
            <h1 id="category-title" class="text-4xl md:text-5xl font-bold mb-4 fade-in">
                <span id="category-name">Category</span>
            </h1>
            <p class="text-gray-500 dark:text-gray-400 fade-in fade-in-delay-2" id="quote-count-container">
                <span id="quote-count">8480</span> quotes in this category
            </p>
        </section>

        <!-- Content Grid (Left-Right Layout) -->
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Left Column (Quotes List) -->
            <section class="lg:w-2/3">
                <!-- 直接嵌入quotes-list组件 -->
                <div class="quotes-list-component">
                    <h2 class="text-2xl font-bold mb-6 flex items-center">
                        <i class="fas fa-quote-right text-yellow-500 mr-2" aria-hidden="true"></i>
                        <span id="quotes-list-title"></span>
                        <span id="quotes-count-display" class="ml-2 text-sm text-gray-500 dark:text-gray-400 font-normal"></span>
                    </h2>

                    <!-- Quotes List (Cards) -->
                    <div class="space-y-6" id="quotes-list" role="feed" aria-busy="true" aria-label="Quotes list">
                        <!-- Loading spinner (will be replaced by quotes) -->
                        <div class="flex justify-center py-12">
                            <div class="loading-spinner" role="status">
                                <span class="sr-only">Loading quotes...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div id="pagination-container">
                    <!-- Pagination component will be loaded here -->
                </div>
            </section>

            <!-- Right Column (Sidebar) -->
            <aside class="lg:w-1/3" role="complementary" aria-label="Sidebar">
                <div id="popular-topics-container">
                    <!-- Popular topics component will be loaded here -->
                </div>
            </aside>
        </div>
    </main>

    <!-- Footer (will be loaded by component loader) -->
    <footer id="footer-container" role="contentinfo"></footer>

    <!-- JavaScript -->
    <!-- Debug Script -->
    <script src="js/debug.js"></script>

    <!-- Component Loader -->
    <script src="js/component-loader.js"></script>

    <!-- Mock Data -->
    <script src="js/mock-data.js"></script>

    <!-- API Client -->
    <script src="js/api-client.js"></script>

    <!-- Core Modules -->
    <script src="js/theme.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/mobile-menu.js"></script>
    <script src="js/components/pagination.js"></script>
    <script src="js/components/quote-card.js"></script>
    <script src="js/components/breadcrumb.js"></script>
    <script src="js/social-meta.js"></script>

    <!-- Global Fix Script -->
    <script src="js/global-fix.js"></script>

    <!-- Page Specific Script -->
    <script src="js/pages/category.js"></script>

    <!-- Direct DOM Manipulation -->
    <script>
        // 直接修改DOM
        window.addEventListener('load', function() {
            // 等待一下，然后直接修改DOM
            setTimeout(function() {
                // 尝试获取引用计数元素
                const quoteCountElement = document.getElementById('quote-count');
                if (quoteCountElement) {
                    // 如果元素存在，尝试从页面状态中获取计数
                    if (window.pageState && window.pageState.totalQuotes > 0) {
                        quoteCountElement.textContent = window.pageState.totalQuotes;
                        console.log('Updated quote count from window.pageState:', window.pageState.totalQuotes);
                    } else {
                        // 如果页面状态不可用，尝试直接设置一个值
                        quoteCountElement.textContent = '8480';
                        console.log('Set quote count to hardcoded value: 8480');
                    }
                } else {
                    console.error('Quote count element not found in direct DOM manipulation!');
                }
            }, 3000);
        });
    </script>
</body>
</html>
